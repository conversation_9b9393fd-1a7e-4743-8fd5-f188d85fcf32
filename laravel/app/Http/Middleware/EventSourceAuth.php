<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Lara<PERSON>\Passport\TokenRepository;
use <PERSON><PERSON>\Passport\Guards\TokenGuard;

class EventSourceAuth
{
    protected $tokenRepository;

    public function __construct(TokenRepository $tokenRepository)
    {
        $this->tokenRepository = $tokenRepository;
    }

    public function handle(Request $request, Closure $next)
    {
        // Check if token is in query parameter
//        if ($request->has('access_token')) {
//            $token = $request->get('access_token');
//            $request->headers->set('Authorization', 'Bearer ' . $token);
//        }

        return $next($request);
    }
}
