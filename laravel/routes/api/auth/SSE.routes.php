<?php

use App\Http\Controllers\SSEController;
use Illuminate\Support\Facades\Route;

// Server-Sent Events routes for progress tracking
// Route::get('/sse/progress', [SSEController::class, 'progressStream'])->name('sse.progress');
// Route::post('/sse/register-progress', [SSEController::class, 'registerProgress'])->name('sse.register');
// Route::post('/sse/unregister-progress', [SSEController::class, 'unregisterProgress'])->name('sse.unregister');
// Route::post('/sse/update-progress', [SSEController::class, 'updateProgress'])->name('sse.update');
